-- Initial database schema for Remix + Google One Tap authentication

-- Users table
CREATE TABLE users (
  id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email        TEXT UNIQUE NOT NULL,
  google_sub   TEXT UNIQUE,          -- Google ID (sub)
  name         TEXT,
  avatar_url   TEXT,
  password_hash TEXT,                -- nullable (Google-only accounts)
  created_at   TIMESTAMPTZ DEFAULT NOW()
);

-- Sessions table for database-backed sessions
CREATE TABLE sessions (
  id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id      UUID REFERENCES users(id) ON DELETE CASCADE,
  expires_at   TIMESTAMPTZ NOT NULL,
  created_at   TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_sessions_user_id ON sessions (user_id);
CREATE INDEX idx_sessions_expires_at ON sessions (expires_at);
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_google_sub ON users (google_sub);
