import type { FilterValue, Order, User } from "~/types/common";
import { DataTable, type TableColumn } from "./DataTable";
import type { PaginationInfo } from "./Pagination";
import { type FilterField, SearchFilter } from "./SearchFilter";

export interface SearchableDataTableProps<T = Record<string, unknown>> {
  // Data and pagination
  data: T[];
  pagination?: PaginationInfo;
  loading?: boolean;

  // Table configuration
  columns: TableColumn<T>[];
  emptyMessage?: string;

  // Search and filter configuration
  filterFields?: FilterField[];
  showSearch?: boolean;
  searchPlaceholder?: string;

  // Event handlers
  onFilterChange?: (filters: FilterValue) => void;
  onSort?: (sortBy: string, sortOrder: "asc" | "desc") => void;
  onPageChange?: (page: number) => void;

  // Styling
  className?: string;
  tableClassName?: string;
  filterClassName?: string;

  // Layout options
  showFilters?: boolean;
  showPagination?: boolean;

  // Header content
  title?: string;
  subtitle?: string;
  headerActions?: React.ReactNode;
}

export function SearchableDataTable<T = Record<string, unknown>>({
  data,
  pagination,
  loading = false,
  columns,
  emptyMessage = "No data available",
  filterFields = [],
  showSearch = true,
  searchPlaceholder = "Search...",
  onFilterChange,
  onSort,
  onPageChange,
  className = "",
  tableClassName = "",
  filterClassName = "",
  showFilters = true,
  showPagination = true,
  title,
  subtitle,
  headerActions,
}: SearchableDataTableProps<T>) {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      {(title || subtitle || headerActions) && (
        <div className="bg-white shadow">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex items-center justify-between">
                <div>
                  {title && <h1 className="text-3xl font-bold text-gray-900">{title}</h1>}
                  {subtitle && <p className="mt-2 text-gray-600">{subtitle}</p>}
                </div>
                {headerActions && (
                  <div className="flex items-center space-x-4">{headerActions}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (filterFields.length > 0 || showSearch) && (
        <SearchFilter
          fields={filterFields}
          onFilterChange={onFilterChange}
          showSearch={showSearch}
          searchPlaceholder={searchPlaceholder}
          className={filterClassName}
        />
      )}

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={data}
        pagination={pagination}
        loading={loading}
        emptyMessage={emptyMessage}
        onSort={onSort}
        onPageChange={onPageChange}
        className={tableClassName}
        showPagination={showPagination}
      />
    </div>
  );
}

// Pre-configured components for common use cases

export function UserDataTable({
  data,
  pagination,
  loading,
  onFilterChange,
  onSort,
  onPageChange,
  onUserAction,
  ...props
}: Omit<SearchableDataTableProps, "columns" | "filterFields"> & {
  onUserAction?: (action: string, user: User) => void;
}) {
  const columns: TableColumn[] = [
    {
      key: "name",
      label: "User",
      sortable: true,
      render: (_, user) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{user.name}</div>
            <div className="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: "credits",
      label: "Credits",
      sortable: true,
      render: (credits) => credits?.toLocaleString(undefined) || 0,
      className: "text-right",
    },
    {
      key: "isAffiliate",
      label: "Status",
      render: (isAffiliate) => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            isAffiliate ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
          }`}
        >
          {isAffiliate ? "Affiliate" : "Regular"}
        </span>
      ),
    },
    {
      key: "createdAt",
      label: "Joined",
      sortable: true,
      render: (date) =>
        new Date(date).toLocaleDateString(undefined, {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, user) => (
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => onUserAction?.("view", user)}
            className="text-blue-600 hover:text-blue-900 text-sm font-medium"
          >
            View
          </button>
          <button
            type="button"
            onClick={() => onUserAction?.("edit", user)}
            className="text-green-600 hover:text-green-900 text-sm font-medium"
          >
            Edit
          </button>
          <button
            type="button"
            onClick={() => onUserAction?.("delete", user)}
            className="text-red-600 hover:text-red-900 text-sm font-medium"
          >
            Delete
          </button>
        </div>
      ),
    },
  ];

  const filterFields: FilterField[] = [
    {
      key: "isAffiliate",
      label: "Affiliate Status",
      type: "select",
      options: [
        { value: "true", label: "Affiliates Only" },
        { value: "false", label: "Non-Affiliates Only" },
      ],
    },
    {
      key: "minCredits",
      label: "Min Credits",
      type: "number",
      placeholder: "0",
    },
    {
      key: "maxCredits",
      label: "Max Credits",
      type: "number",
      placeholder: "1000",
    },
  ];

  return (
    <SearchableDataTable
      data={data}
      pagination={pagination}
      loading={loading}
      columns={columns}
      filterFields={filterFields}
      onFilterChange={onFilterChange}
      onSort={onSort}
      onPageChange={onPageChange}
      searchPlaceholder="Search users by name or email..."
      emptyMessage="No users found"
      {...props}
    />
  );
}

export function OrderDataTable({
  data,
  pagination,
  loading,
  onFilterChange,
  onSort,
  onPageChange,
  onOrderAction,
  ...props
}: Omit<SearchableDataTableProps, "columns" | "filterFields"> & {
  onOrderAction?: (action: string, order: Order) => void;
}) {
  const columns: TableColumn[] = [
    {
      key: "orderNo",
      label: "Order",
      sortable: true,
      render: (_, order) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{order.orderNo}</div>
          <div className="text-sm text-gray-500 capitalize">{order.billingProvider}</div>
        </div>
      ),
    },
    {
      key: "userEmail",
      label: "Customer",
      sortable: true,
      render: (_, order) => (
        <div>
          <div className="text-sm text-gray-900">{order.userEmail}</div>
          <div className="text-sm text-gray-500 font-mono text-xs">{order.userUuid}</div>
        </div>
      ),
    },
    {
      key: "totalAmount",
      label: "Amount",
      sortable: true,
      render: (amount, order) => (
        <div className="text-right">
          <div className="text-sm font-medium text-gray-900">${parseFloat(amount).toFixed(2)}</div>
          <div className="text-sm text-gray-500">{order.currency}</div>
        </div>
      ),
      className: "text-right",
    },
    {
      key: "status",
      label: "Status",
      render: (status) => {
        const colorMap = {
          succeeded: "bg-green-100 text-green-800",
          pending: "bg-yellow-100 text-yellow-800",
          failed: "bg-red-100 text-red-800",
        };
        return (
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              colorMap[status as keyof typeof colorMap] || "bg-gray-100 text-gray-800"
            }`}
          >
            {status}
          </span>
        );
      },
    },
    {
      key: "createdAt",
      label: "Date",
      sortable: true,
      render: (date) =>
        new Date(date).toLocaleDateString(undefined, {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, order) => (
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => onOrderAction?.("view", order)}
            className="text-blue-600 hover:text-blue-900 text-sm font-medium"
          >
            View
          </button>
          <button
            type="button"
            onClick={() => onOrderAction?.("refund", order)}
            className="text-green-600 hover:text-green-900 text-sm font-medium"
          >
            Refund
          </button>
          <button
            type="button"
            onClick={() => onOrderAction?.("cancel", order)}
            className="text-red-600 hover:text-red-900 text-sm font-medium"
          >
            Cancel
          </button>
        </div>
      ),
    },
  ];

  const filterFields: FilterField[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "pending", label: "Pending" },
        { value: "succeeded", label: "Succeeded" },
        { value: "failed", label: "Failed" },
      ],
    },
    {
      key: "minAmount",
      label: "Min Amount",
      type: "number",
      placeholder: "0.00",
    },
    {
      key: "maxAmount",
      label: "Max Amount",
      type: "number",
      placeholder: "1000.00",
    },
    {
      key: "dateFrom",
      label: "Date From",
      type: "date",
    },
    {
      key: "dateTo",
      label: "Date To",
      type: "date",
    },
  ];

  return (
    <SearchableDataTable
      data={data}
      pagination={pagination}
      loading={loading}
      columns={columns}
      filterFields={filterFields}
      onFilterChange={onFilterChange}
      onSort={onSort}
      onPageChange={onPageChange}
      searchPlaceholder="Search orders by number or email..."
      emptyMessage="No orders found"
      {...props}
    />
  );
}
