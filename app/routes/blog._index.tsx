import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Link, useLoaderData, useSearchParams } from "@remix-run/react";
import { Calendar, Clock, Search, Tag, User } from "lucide-react";
import UnifiedLayout from "~/shared/components/layouts/unified-layout";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";

// Temporarily disabled - Keystatic removed for bundle size optimization
// import {
//   type BlogPost,
//   formatDate,
//   generateExcerpt,
//   getAllPosts,
//   getReadingTime,
// } from "~/lib/content/keystatic";

// Temporary types for blog functionality
type BlogPost = {
  slug: string;
  title: string;
  excerpt: string;
  content: any;
  author: string;
  publishedDate: string;
  category: string;
  tags: string[];
  featuredImage?: string;
  status: string;
};

const formatDate = (date: string) => new Date(date).toLocaleDateString();
const getReadingTime = (content: any) => 5; // Default reading time
const generateExcerpt = (content: any) => "Sample excerpt"; // Default excerpt

export const meta: MetaFunction = () => {
  return [
    { title: "Blog - AI SaaS Starter" },
    {
      name: "description",
      content:
        "Discover the latest insights, tutorials, and updates about AI tools, web development, and SaaS best practices.",
    },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Temporarily disabled - return empty blog data
  return json({
    posts: [],
    categories: [],
    featuredPosts: [],
    searchQuery: null,
    selectedCategory: null,
  });
}

function BlogCard({ post }: { post: BlogPost }) {
  return (
    <Card className="group overflow-hidden border-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-gray-900/80 transition-all duration-300 hover:scale-105 hover:shadow-2xl">
      {post.featuredImage && (
        <div className="aspect-video overflow-hidden relative">
          <img
            src={post.featuredImage}
            alt={post.title}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
      )}
      <CardHeader>
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
          <div className="flex items-center gap-1 bg-blue-500/10 px-2 py-1 rounded-full">
            <Calendar className="h-3 w-3" />
            {formatDate(post.publishedDate)}
          </div>
          <div className="flex items-center gap-1 bg-purple-500/10 px-2 py-1 rounded-full">
            <Clock className="h-3 w-3" />
            {getReadingTime(post.content)} min read
          </div>
        </div>
        <CardTitle className="line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-lg font-semibold">
          <Link to={`/blog/${post.slug}`}>{post.title}</Link>
        </CardTitle>
        <CardDescription className="line-clamp-3 text-gray-600 dark:text-gray-300">
          {post.excerpt || generateExcerpt(post.content)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {post.author}
            </span>
          </div>
          <Badge
            variant="secondary"
            className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 text-blue-700 dark:text-blue-300 border-0"
          >
            {post.category.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
          </Badge>
        </div>
        <div className="flex flex-wrap gap-2 mt-3">
          {post.tags.slice(0, 3).map((tag) => (
            <Link
              key={tag}
              to={`/blog?q=${encodeURIComponent(tag)}`}
              className="text-xs bg-gray-100 dark:bg-gray-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 px-2 py-1 rounded-full text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              #{tag}
            </Link>
          ))}
        </div>
      </CardContent>

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </Card>
  );
}

export default function BlogIndex() {
  const { posts, categories, featuredPosts, searchQuery, selectedCategory } =
    useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();

  const handleSearch = (query: string) => {
    const newSearchParams = new URLSearchParams(searchParams);
    if (query) {
      newSearchParams.set("q", query);
    } else {
      newSearchParams.delete("q");
    }
    newSearchParams.delete("category");
    setSearchParams(newSearchParams);
  };

  const handleCategoryFilter = (category: string) => {
    const newSearchParams = new URLSearchParams(searchParams);
    if (category === selectedCategory) {
      newSearchParams.delete("category");
    } else {
      newSearchParams.set("category", category);
    }
    newSearchParams.delete("q");
    setSearchParams(newSearchParams);
  };

  return (
    <UnifiedLayout
      hero={{
        title: "From the Blog",
        description: "Latest news, articles, and insights from our team.",
        backgroundPattern: "gradient",
      }}
    >
      {/* Enhanced Search Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900/50">
        <div className="max-w-4xl mx-auto px-4">
          <div className="max-w-lg mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-20 group-hover:opacity-30 transition duration-300" />
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
                <Input
                  type="search"
                  placeholder="Search articles..."
                  defaultValue={searchQuery || ""}
                  className="pl-12 pr-4 py-4 text-lg border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl shadow-lg focus:shadow-xl transition-all"
                  onChange={(e) => {
                    const value = e.target.value;
                    const timeoutId = setTimeout(() => handleSearch(value), 300);
                    return () => clearTimeout(timeoutId);
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Featured Posts */}
            {featuredPosts.length > 0 && !searchQuery && !selectedCategory && (
              <section className="mb-12">
                <h2 className="text-2xl font-bold mb-6">Featured Articles</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {featuredPosts.slice(0, 2).map((post) => (
                    <BlogCard key={post.slug} post={post} />
                  ))}
                </div>
              </section>
            )}

            {/* All Posts */}
            <section>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold">
                  {searchQuery
                    ? `Search Results for "${searchQuery}"`
                    : selectedCategory
                      ? `${selectedCategory} Articles`
                      : "Latest Articles"}
                </h2>
                <span className="text-muted-foreground">
                  {posts.length} article{posts.length !== 1 ? "s" : ""}
                </span>
              </div>

              {posts.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground mb-4">
                    No articles found matching your criteria.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchParams({});
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {posts.map((post) => (
                    <BlogCard key={post.slug} post={post} />
                  ))}
                </div>
              )}
            </section>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-8">
              {/* Enhanced Categories */}
              <Card className="border-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-lg font-semibold">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <Tag className="h-4 w-4 text-white" />
                    </div>
                    Categories
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <button
                        key={category.slug}
                        onClick={() => handleCategoryFilter(category.slug)}
                        className={`w-full text-left px-4 py-3 rounded-xl text-sm transition-all duration-300 ${
                          selectedCategory === category.slug
                            ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg scale-105"
                            : "hover:bg-gray-100 dark:hover:bg-gray-800 hover:scale-102"
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{category.name}</span>
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              selectedCategory === category.slug
                                ? "bg-white/20 text-white"
                                : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                            }`}
                          >
                            {category.count}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Newsletter Signup */}
              <Card className="border-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 backdrop-blur-sm shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Stay Updated
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    Get the latest articles delivered to your inbox.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-sm focus:shadow-lg transition-all"
                    />
                    <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                      Subscribe
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  );
}
