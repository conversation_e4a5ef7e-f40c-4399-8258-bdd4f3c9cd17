import { Link } from "@remix-run/react";
import { Check, Loader2, Star } from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import ContentSection from "./content-section";

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  currency: string;
  interval: "month" | "year" | "one-time";
  features: string[];
  popular?: boolean;
  buttonText: string;
  buttonVariant?: "default" | "outline" | "secondary";
  credits?: number;
  stripePriceId?: string;
}

export interface PricingGroup {
  name: string;
  title: string;
  label?: string;
}

export interface PricingProps {
  title?: string;
  description?: string;
  groups?: PricingGroup[];
  plans?: PricingPlan[];
  className?: string;
}

const defaultPlans: PricingPlan[] = [
  {
    id: "free",
    name: "Free",
    description: "Perfect for getting started with AI tools",
    price: 0,
    currency: "USD",
    interval: "month",
    features: [
      "100 AI credits per month",
      "Basic text generation",
      "Community support",
      "Standard response time",
    ],
    buttonText: "Get Started",
    buttonVariant: "outline",
    credits: 100,
  },
  {
    id: "pro",
    name: "Pro",
    description: "Best for professionals and small teams",
    price: 29,
    originalPrice: 39,
    currency: "USD",
    interval: "month",
    features: [
      "5,000 AI credits per month",
      "All AI models access",
      "Image generation",
      "Priority support",
      "Advanced analytics",
      "API access",
    ],
    popular: true,
    buttonText: "Start Pro Trial",
    buttonVariant: "default",
    credits: 5000,
    stripePriceId: "price_pro_monthly",
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "For large teams and organizations",
    price: 99,
    currency: "USD",
    interval: "month",
    features: [
      "Unlimited AI credits",
      "Custom AI models",
      "White-label solution",
      "Dedicated support",
      "SLA guarantee",
      "Custom integrations",
      "Advanced security",
    ],
    buttonText: "Contact Sales",
    buttonVariant: "outline",
    credits: -1, // Unlimited
  },
];

export default function Pricing({
  title = "Simple, Transparent Pricing",
  description = "Choose the perfect plan for your needs. Upgrade or downgrade at any time.",
  groups,
  plans = defaultPlans,
  className = "",
}: PricingProps) {
  const [selectedGroup, setSelectedGroup] = useState(groups?.[0]?.name || "monthly");
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleCheckout = async (plan: PricingPlan) => {
    if (plan.price === 0) {
      // Handle free plan signup
      window.location.href = "/ai-tools";
      return;
    }

    if (plan.id === "enterprise") {
      // Handle enterprise contact
      window.location.href = "/contact";
      return;
    }

    setIsLoading(plan.id);

    try {
      // Simulate checkout process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Redirect to actual checkout
      window.location.href = `/checkout?plan=${plan.id}&price=${plan.stripePriceId}`;
    } catch (error) {
      console.error("Checkout failed:", error);
    } finally {
      setIsLoading(null);
    }
  };

  const formatPrice = (price: number, currency: string, interval: string) => {
    if (price === 0) return "Free";

    const formatter = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
    });

    const formattedPrice = formatter.format(price);

    if (interval === "one-time") return formattedPrice;
    return `${formattedPrice}/${interval === "year" ? "year" : "month"}`;
  };

  const getDiscountedPrice = (plan: PricingPlan) => {
    if (selectedGroup === "yearly" && plan.interval === "month") {
      return Math.floor(plan.price * 12 * 0.8); // 20% discount for annual
    }
    return plan.price;
  };

  return (
    <ContentSection
      title={title}
      description={description}
      background="gradient"
      decorations={true}
      padding="lg"
      headerSpacing="lg"
      className={className}
    >
      {/* Billing Toggle */}
      {groups && groups.length > 0 && (
        <div className="flex justify-center mb-12">
          <div className="bg-muted p-1 rounded-lg">
            <RadioGroup value={selectedGroup} onValueChange={setSelectedGroup} className="flex">
              {groups.map((group) => (
                <div key={group.name} className="relative">
                  <RadioGroupItem value={group.name} id={group.name} className="peer sr-only" />
                  <Label
                    htmlFor={group.name}
                    className="flex items-center gap-2 px-6 py-3 rounded-md cursor-pointer font-semibold text-muted-foreground peer-data-[state=checked]:bg-background peer-data-[state=checked]:text-foreground peer-data-[state=checked]:shadow-sm transition-all"
                  >
                    {group.title}
                    {group.label && (
                      <Badge variant="secondary" className="text-xs">
                        {group.label}
                      </Badge>
                    )}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        </div>
      )}

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16 max-w-7xl mx-auto">
        {plans.map((plan) => {
          const currentPrice = getDiscountedPrice(plan);
          const isYearly = selectedGroup === "yearly" && plan.interval === "month";

          return (
            <Card
              key={plan.id}
              className={`relative transition-all duration-200 hover:shadow-md ${
                plan.popular
                  ? "ring-2 ring-primary shadow-lg"
                  : "border-border hover:border-primary/20"
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <Badge className="bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm">
                    Most Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-8">
                <CardTitle className="text-3xl font-bold mb-2">{plan.name}</CardTitle>
                <CardDescription className="text-base text-muted-foreground mb-6">
                  {plan.description}
                </CardDescription>

                <div className="mt-6">
                  <div className="flex items-end justify-center gap-2 mb-2">
                    {plan.originalPrice && plan.originalPrice > currentPrice && (
                      <span className="text-xl text-muted-foreground line-through">
                        {formatPrice(
                          plan.originalPrice,
                          plan.currency,
                          isYearly ? "year" : plan.interval
                        )}
                      </span>
                    )}
                    <span className="text-4xl font-bold text-foreground">
                      {formatPrice(currentPrice, plan.currency, isYearly ? "year" : plan.interval)}
                    </span>
                  </div>
                  {isYearly && plan.price > 0 && (
                    <p className="text-sm text-success font-medium mt-2">
                      Save 20% with annual billing
                    </p>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={`${feature.slice(0, 20)}-${index}`} className="flex items-start gap-3">
                      <Check className="w-5 h-5 text-primary mt-0.5 shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full transition-all duration-200 ${
                    plan.popular ? "bg-primary hover:bg-primary/90 text-primary-foreground" : ""
                  }`}
                  variant={plan.popular ? "default" : plan.buttonVariant}
                  onClick={() => handleCheckout(plan)}
                  disabled={isLoading === plan.id}
                >
                  {isLoading === plan.id ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    plan.buttonText
                  )}
                </Button>

                {plan.credits && plan.credits > 0 && (
                  <div className="text-center mt-4 p-3 bg-muted/30 rounded-lg">
                    <p className="text-sm font-medium text-foreground">
                      {plan.credits.toLocaleString()} credits included
                    </p>
                  </div>
                )}
                {plan.credits === -1 && (
                  <div className="text-center mt-4 p-3 bg-muted/30 rounded-lg">
                    <p className="text-sm font-medium text-foreground">Unlimited credits</p>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* FAQ or Additional Info */}
      <div className="text-center bg-muted/30 rounded-xl p-8 max-w-2xl mx-auto">
        <h3 className="text-xl font-semibold mb-3">Need Something Different?</h3>
        <p className="text-muted-foreground mb-6">
          Looking for a custom plan or have questions about our pricing?
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="outline" asChild>
            <Link to="/contact">Contact Sales</Link>
          </Button>
          <Button variant="ghost" asChild>
            <Link to="/faq">View FAQ</Link>
          </Button>
        </div>
      </div>
    </ContentSection>
  );
}
