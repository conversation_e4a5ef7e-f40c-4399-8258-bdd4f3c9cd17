/**
 * Order processing service
 */

import type <PERSON><PERSON> from "stripe";
import type { Database } from "~/lib/db/db";
import { findOrderBySessionId, updateOrderStatus } from "~/models/order";
import { CreditsTransType, increaseCredits } from "~/services/user-management.server";

/**
 * <PERSON><PERSON> completed Stripe checkout session
 */
export async function handleOrderSession(
  session: Stripe.Checkout.Session,
  db: Database
): Promise<boolean> {
  try {
    if (!session.id) {
      console.error("No session ID provided");
      return false;
    }

    // Find order by Stripe session ID
    const order = await findOrderBySessionId(session.id, db);
    if (!order) {
      console.error(`Order not found for session ${session.id}`);
      return false;
    }

    // Update order status to paid
    await updateOrderStatus(order.order_no, "paid", db);

    // Add credits to user account if applicable
    if (order.credits && order.credits > 0) {
      await increaseCredits(
        {
          user_uuid: order.user_uuid,
          trans_type: CreditsTransType.Purchase,
          credits: order.credits,
          description: `Purchase: ${order.product_name}`,
        },
        db
      );
    }

    console.log(`Order ${order.order_no} processed successfully`);
    return true;
  } catch (error) {
    console.error("Error handling order session:", error);
    return false;
  }
}
