import { Link } from "@remix-run/react";
import { CheckCircle, CreditCard, Home, Mail } from "lucide-react";
import UnifiedLayout from "~/shared/components/layouts/unified-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export default function PaymentSuccessPage() {
  return (
    <UnifiedLayout
      hero={{
        title: "🎉 Payment Successful!",
        description: "Thank you for your purchase. Your credits have been added to your account.",
        buttons: [
          {
            text: "Go to Dashboard",
            href: "/console/dashboard",
            variant: "primary",
          },
          {
            text: "View Orders",
            href: "/console/orders",
            variant: "outline",
          },
        ],
      }}
    >
      <section className="py-16">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-2xl font-bold text-green-600 dark:text-green-400">
                Payment Successful!
              </CardTitle>
              <CardDescription className="text-lg">
                Your transaction has been completed successfully
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
                  <Mail className="h-5 w-5" />
                  <span>Confirmation email will be sent shortly</span>
                </div>

                <p className="text-gray-600 dark:text-gray-400">
                  If you have any questions about your purchase, please don't hesitate to contact
                  our support team.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-6">
                <Button asChild className="w-full">
                  <Link to="/" className="flex items-center justify-center gap-2">
                    <Home className="h-4 w-4" />
                    Go to Homepage
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link to="/checkout" className="flex items-center justify-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Make Another Payment
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
