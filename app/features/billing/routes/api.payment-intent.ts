import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import Strip<PERSON> from "stripe";
import { getStripeClient } from "~/lib/payment/stripe.server";

// Define expected form data structure (optional but good practice)
interface PaymentIntentFormData {
  amount: string | null;
  currency: string | null;
}

// Define possible JSON response types
type ActionResponse = { clientSecret: string } | { error: string; details?: unknown };

export async function action({ request, context }: ActionFunctionArgs): Promise<Response> {
  const stripeSecretKey = context.cloudflare.env.STRIPE_SECRET_KEY;
  if (!stripeSecretKey) {
    return json<ActionResponse>({ error: "Stripe secret key not configured" }, { status: 500 });
  }
  const stripe = getStripeClient(stripeSecretKey);

  try {
    const formData = await request.formData();
    const rawAmount = formData.get("amount");
    const rawCurrency = formData.get("currency");

    const amount = Number(rawAmount);
    const currency = typeof rawCurrency === "string" ? rawCurrency : "usd";

    if (Number.isNaN(amount) || amount <= 0) {
      return json<ActionResponse>({ error: "Invalid amount" }, { status: 400 });
    }

    const params: Stripe.PaymentIntentCreateParams = {
      amount,
      currency,
      automatic_payment_methods: {
        enabled: true,
      },
    };

    const paymentIntent: Stripe.PaymentIntent = await stripe.paymentIntents.create(params);

    if (!paymentIntent.client_secret) {
      return json<ActionResponse>(
        { error: "Failed to create PaymentIntent: client_secret is missing." },
        { status: 500 }
      );
    }

    return json<ActionResponse>({ clientSecret: paymentIntent.client_secret });
  } catch (error: unknown) {
    console.error("Error creating PaymentIntent:", error);
    let errorMessage = "Failed to create PaymentIntent";
    if (error instanceof Stripe.errors.StripeError) {
      errorMessage = error.message;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }
    return json<ActionResponse>({ error: errorMessage }, { status: 500 });
  }
}
