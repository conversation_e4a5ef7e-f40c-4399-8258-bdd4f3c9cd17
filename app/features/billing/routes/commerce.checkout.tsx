import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json, useF<PERSON>cher, useLoaderData } from "@remix-run/react";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, type Stripe } from "@stripe/stripe-js";
import { CreditCard, Lock, Shield } from "lucide-react";
import { useEffect, useState } from "react";
import CheckoutFormComponent from "~/components/checkout-form";
import UnifiedLayout from "~/shared/components/layouts/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

// StripePromise will be initialized once and passed to Elements
let stripePromise: Promise<Stripe | null>; // This global/module-level variable can be problematic in serverless environments for concurrency.
// Consider initializing it within StripeCheckoutWrapper or passing it down.
// For now, leaving as is per original structure.

interface StripeCheckoutWrapperProps {
  stripePublicKey: string;
  clientSecret: string;
}

function StripeCheckoutWrapper({ stripePub<PERSON>Key, clientSecret }: StripeCheckoutWrapperProps) {
  // Initialize stripePromise only once per instance or based on publicKey
  if (!stripePromise) {
    stripePromise = loadStripe(stripePublicKey);
  }

  if (!clientSecret) {
    return <div>Loading Payment Details...</div>;
  }

  return (
    <Elements stripe={stripePromise} options={{ clientSecret }}>
      <CheckoutFormComponent />
    </Elements>
  );
}

// Define loader response type
interface LoaderData {
  stripePublicKey: string;
}

export async function loader({ context }: LoaderFunctionArgs) {
  const stripePublicKey = context.cloudflare.env.STRIPE_PUBLIC_KEY;
  if (!stripePublicKey) {
    // This will be caught by Remix's error boundary
    throw new Response("Stripe public key not configured", { status: 500 });
  }
  return json({ stripePublicKey });
}

// Define the type for the data fetched by useFetcher, matching ActionResponse from create-payment-intent.tsx
type PaymentIntentFetcherData =
  | { clientSecret: string }
  | { error: string; details?: unknown }
  | undefined;

export default function CheckoutPage() {
  const { stripePublicKey } = useLoaderData<typeof loader>() as LoaderData; // Type assertion for loader data
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const fetcher = useFetcher<PaymentIntentFetcherData>();

  useEffect(() => {
    // Ensure clientSecret is not already set before fetching
    if (fetcher.state === "idle" && !fetcher.data && !clientSecret) {
      fetcher.submit(
        { amount: "1099", currency: "usd" }, // Example: make amount/currency configurable or from cart
        { method: "post", action: "/api/payment-intent" }
      );
    }

    // Check if fetcher.data is not null before accessing its properties
    if (fetcher.data) {
      if ("clientSecret" in fetcher.data && fetcher.data.clientSecret) {
        setClientSecret(fetcher.data.clientSecret);
      } else if ("error" in fetcher.data && fetcher.data.error) {
        console.error("Error fetching client secret:", fetcher.data.error);
        // Optionally, set an error message in state to display in the UI
        // setMessage(fetcher.data.error);
      }
    }
  }, [fetcher, clientSecret]);

  if (!stripePublicKey) {
    return (
      <UnifiedLayout
        hero={{
          title: "Configuration Error",
          description: "Payment system is not properly configured. Please contact support.",
        }}
      >
        <section className="py-16 text-center">
          <Card className="max-w-md mx-auto">
            <CardContent className="pt-6">
              <p className="text-red-600 dark:text-red-400">
                Error: Stripe public key is missing. Configuration error.
              </p>
            </CardContent>
          </Card>
        </section>
      </UnifiedLayout>
    );
  }

  if (fetcher.state === "loading" || !clientSecret) {
    return (
      <UnifiedLayout
        hero={{
          title: "Preparing Checkout",
          description: "Setting up your secure payment session...",
        }}
      >
        <section className="py-16 text-center">
          <Card className="max-w-md mx-auto">
            <CardContent className="pt-6">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                {fetcher.state === "loading"
                  ? "Loading payment details..."
                  : "Initializing secure payment session..."}
              </p>
            </CardContent>
          </Card>
        </section>
      </UnifiedLayout>
    );
  }

  if (fetcher.state === "idle" && fetcher.data && "error" in fetcher.data) {
    return (
      <UnifiedLayout
        hero={{
          title: "Payment Error",
          description: "There was an issue initializing the payment system.",
        }}
      >
        <section className="py-16 text-center">
          <Card className="max-w-md mx-auto">
            <CardContent className="pt-6">
              <p className="text-red-600 dark:text-red-400 mb-4">
                Error initializing payment: {fetcher.data.error}
              </p>
              <p className="text-gray-600 dark:text-gray-400">Please refresh and try again.</p>
            </CardContent>
          </Card>
        </section>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout
      hero={{
        title: "Checkout",
        description: "Complete your purchase securely.",
      }}
    >
      <section className="py-16">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-3 text-2xl font-bold">
                <CreditCard className="h-6 w-6 text-blue-600" />
                Payment Details
              </CardTitle>
              <CardDescription className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
                <Shield className="h-4 w-4 text-green-500" />
                Your payment is protected by industry-standard encryption
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StripeCheckoutWrapper
                stripePublicKey={stripePublicKey}
                clientSecret={clientSecret}
              />
            </CardContent>
          </Card>

          {/* Security Features */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Lock className="h-4 w-4 text-green-500" />
              SSL Encrypted
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Shield className="h-4 w-4 text-green-500" />
              PCI Compliant
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <CreditCard className="h-4 w-4 text-green-500" />
              Secure Processing
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
