import { Link, useSearchParams } from "@remix-run/react";
import { AlertTriangle, Home, RefreshCw, XCircle } from "lucide-react";
import UnifiedLayout from "~/shared/components/layouts/unified-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export default function PaymentFailurePage() {
  const [searchParams] = useSearchParams();
  const paymentIntentClientSecret = searchParams.get("payment_intent_client_secret");
  const redirectStatus = searchParams.get("redirect_status");

  let errorMessage = "Your payment could not be processed. Please try again.";

  if (redirectStatus === "failed") {
    errorMessage =
      "The payment attempt failed. Please try a different payment method or contact support.";
  } else if (paymentIntentClientSecret) {
    errorMessage = "There was an issue with your payment. Please check the details and try again.";
  }

  return (
    <UnifiedLayout
      hero={{
        title: "⚠️ Payment Failed",
        description:
          "Unfortunately, we couldn't process your payment. Please try again or contact support.",
        buttons: [
          {
            text: "Try Again",
            href: "/commerce/checkout",
            variant: "primary",
          },
          {
            text: "Contact Support",
            href: "/contact",
            variant: "outline",
          },
        ],
      }}
    >
      <section className="py-16">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-2xl font-bold text-red-600 dark:text-red-400">
                Payment Failed
              </CardTitle>
              <CardDescription className="text-lg">
                We couldn't process your payment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center space-y-4">
                <div className="flex items-start justify-center gap-2 text-gray-600 dark:text-gray-400">
                  <AlertTriangle className="h-5 w-5 mt-0.5 text-yellow-500" />
                  <p className="text-left">{errorMessage}</p>
                </div>

                {paymentIntentClientSecret && (
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Reference:</strong> {paymentIntentClientSecret.substring(0, 30)}...
                    </p>
                  </div>
                )}

                {redirectStatus && (
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Status:</strong> {redirectStatus}
                    </p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-6">
                <Button asChild className="w-full">
                  <Link to="/checkout" className="flex items-center justify-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Try Payment Again
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link to="/" className="flex items-center justify-center gap-2">
                    <Home className="h-4 w-4" />
                    Go to Homepage
                  </Link>
                </Button>
              </div>

              <div className="text-center pt-4 border-t">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Need help? Contact our support team for assistance with your payment.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
