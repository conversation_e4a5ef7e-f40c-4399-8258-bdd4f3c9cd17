import { deepseek } from "@ai-sdk/deepseek";
import { openai } from "@ai-sdk/openai";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { replicate } from "@ai-sdk/replicate";
import type { Ai } from "@cloudflare/workers-types";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import type { LanguageModelV1 } from "ai";
import { extractReasoningMiddleware, wrapLanguageModel } from "ai";

/**
 * AI Provider types
 */
export type AIProvider =
  | "openai"
  | "deepseek"
  | "openrouter"
  | "siliconflow"
  | "replicate"
  | "cloudflare";

/**
 * AI Provider configuration
 */
export interface AIProviderConfig {
  name: string;
  displayName: string;
  models: string[];
  requiresApiKey: boolean;
  supportsReasoning?: boolean;
  reasoningModels?: string[];
  supportsEmbeddings?: boolean;
  supportsImageClassification?: boolean;
}

/**
 * Available AI providers configuration
 */
export const AI_PROVIDERS: Record<AIProvider, AIProviderConfig> = {
  openai: {
    name: "openai",
    displayName: "OpenAI",
    models: ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo", "o1-preview", "o1-mini"],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["o1-preview", "o1-mini"],
  },
  deepseek: {
    name: "deepseek",
    displayName: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder", "deepseek-r1"],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["deepseek-r1"],
  },
  openrouter: {
    name: "openrouter",
    displayName: "OpenRouter",
    models: [
      "deepseek/deepseek-r1",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "google/gemini-pro-1.5",
      "meta-llama/llama-3.1-405b-instruct",
    ],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["deepseek/deepseek-r1"],
  },
  siliconflow: {
    name: "siliconflow",
    displayName: "SiliconFlow",
    models: [
      "deepseek-ai/DeepSeek-R1",
      "Qwen/Qwen2.5-72B-Instruct",
      "meta-llama/Meta-Llama-3.1-405B-Instruct",
    ],
    requiresApiKey: true,
    supportsReasoning: true,
    reasoningModels: ["deepseek-ai/DeepSeek-R1"],
  },
  replicate: {
    name: "replicate",
    displayName: "Replicate",
    models: ["meta/llama-2-70b-chat", "mistralai/mixtral-8x7b-instruct-v0.1"],
    requiresApiKey: true,
  },
  cloudflare: {
    name: "cloudflare",
    displayName: "Cloudflare Workers AI",
    models: [
      "@cf/meta/llama-3.2-3b-instruct",
      "@cf/meta/llama-3-8b-instruct",
      "@cf/meta/llama-guard-3-8b",
      "@cf/meta/llama-2-7b-chat-fp16",
      "@cf/huggingface/distilbert-sst-2-int8",
      "@cf/baai/bge-m3",
    ],
    requiresApiKey: false,
    supportsEmbeddings: true,
    supportsImageClassification: true,
  },
};

/**
 * Environment variables interface for AI providers
 */
export interface AIEnvironmentVariables {
  OPENAI_API_KEY?: string;
  DEEPSEEK_API_KEY?: string;
  OPENROUTER_API_KEY?: string;
  SILICONFLOW_API_KEY?: string;
  SILICONFLOW_BASE_URL?: string;
  REPLICATE_API_TOKEN?: string;
}

/**
 * Cloudflare context interface
 */
export interface CloudflareContext {
  cloudflare?: {
    env?: {
      AI?: Ai;
    };
  };
}

/**
 * Create AI model instance based on provider and model
 */
export function createAIModel(
  provider: AIProvider,
  model: string,
  env: AIEnvironmentVariables,
  context?: CloudflareContext
): LanguageModelV1 {
  let textModel: LanguageModelV1;

  switch (provider) {
    case "openai": {
      if (!env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY is required for OpenAI provider");
      }
      const openaiProvider = openai({
        apiKey: env.OPENAI_API_KEY,
      });
      textModel = openaiProvider(model);
      break;
    }

    case "deepseek": {
      if (!env.DEEPSEEK_API_KEY) {
        throw new Error("DEEPSEEK_API_KEY is required for DeepSeek provider");
      }
      const deepseekProvider = deepseek({
        apiKey: env.DEEPSEEK_API_KEY,
      });
      textModel = deepseekProvider(model);
      break;
    }

    case "openrouter": {
      if (!env.OPENROUTER_API_KEY) {
        throw new Error("OPENROUTER_API_KEY is required for OpenRouter provider");
      }
      const openrouter = createOpenRouter({
        apiKey: env.OPENROUTER_API_KEY,
      });
      textModel = openrouter(model);

      // Add reasoning middleware for supported models
      if (model === "deepseek/deepseek-r1") {
        textModel = wrapLanguageModel({
          model: textModel,
          middleware: extractReasoningMiddleware({
            tagName: "think",
          }),
        });
      }
      break;
    }

    case "siliconflow": {
      if (!env.SILICONFLOW_API_KEY) {
        throw new Error("SILICONFLOW_API_KEY is required for SiliconFlow provider");
      }
      const siliconflow = createOpenAICompatible({
        name: "siliconflow",
        apiKey: env.SILICONFLOW_API_KEY,
        baseURL: env.SILICONFLOW_BASE_URL || "https://api.siliconflow.cn/v1",
      });
      textModel = siliconflow(model);

      // Add reasoning middleware for supported models
      if (model === "deepseek-ai/DeepSeek-R1") {
        textModel = wrapLanguageModel({
          model: textModel,
          middleware: extractReasoningMiddleware({
            tagName: "reasoning_content",
          }),
        });
      }
      break;
    }

    case "replicate": {
      if (!env.REPLICATE_API_TOKEN) {
        throw new Error("REPLICATE_API_TOKEN is required for Replicate provider");
      }
      const replicateProvider = replicate({
        apiToken: env.REPLICATE_API_TOKEN,
      });
      textModel = replicateProvider.textModel(model);
      break;
    }

    case "cloudflare": {
      // Cloudflare AI uses the AI binding, no API key required
      if (!context?.cloudflare?.env?.AI) {
        throw new Error(
          "Cloudflare AI binding is not available. Please provide a valid Cloudflare context."
        );
      }
      // Create a custom language model for Cloudflare AI
      textModel = createCloudflareAIModel(context.cloudflare.env.AI, model);
      break;
    }

    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }

  return textModel;
}

/**
 * Validate if provider and model combination is supported
 */
export function validateProviderModel(provider: AIProvider, model: string): boolean {
  const providerConfig = AI_PROVIDERS[provider];
  if (!providerConfig) {
    return false;
  }
  return providerConfig.models.includes(model);
}

/**
 * Get available models for a provider
 */
export function getProviderModels(provider: AIProvider): string[] {
  const providerConfig = AI_PROVIDERS[provider];
  return providerConfig?.models || [];
}

/**
 * Check if model supports reasoning
 */
export function supportsReasoning(provider: AIProvider, model: string): boolean {
  const providerConfig = AI_PROVIDERS[provider];
  if (!providerConfig.supportsReasoning) {
    return false;
  }
  return providerConfig.reasoningModels?.includes(model) || false;
}

/**
 * Get all available providers
 */
export function getAvailableProviders(): AIProviderConfig[] {
  return Object.values(AI_PROVIDERS);
}

/**
 * Create a custom language model for Cloudflare AI
 */
function createCloudflareAIModel(ai: Ai, model: string): LanguageModelV1 {
  return {
    specificationVersion: "v1",
    provider: "cloudflare",
    modelId: model,

    async doGenerate(options) {
      const { prompt, mode, ...settings } = options;

      // Convert prompt to messages format
      const messages = prompt.map((p: any) => ({
        role: p.role,
        content: p.content,
      }));

      try {
        const response = await ai.run(model, {
          messages,
          max_tokens: settings.maxTokens || 2048,
          temperature: settings.temperature || 0.7,
        });

        return {
          text: response.response || "",
          usage: {
            promptTokens: 0, // Cloudflare AI doesn't provide token counts
            completionTokens: 0,
          },
          finishReason: "stop",
          rawCall: { rawPrompt: prompt, rawSettings: settings },
          rawResponse: { headers: {} },
          response: {
            id: `cf-${Date.now()}`,
            timestamp: new Date(),
            modelId: model,
          },
        };
      } catch (error) {
        throw new Error(
          `Cloudflare AI error: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      }
    },

    async doStream(options) {
      // For now, fall back to generate and return as single chunk
      // Cloudflare AI streaming support can be added later
      const result = await this.doGenerate(options);

      return {
        stream: (async function* () {
          yield {
            type: "text-delta" as const,
            textDelta: result.text || "",
          };
          yield {
            type: "finish" as const,
            finishReason: result.finishReason,
            usage: result.usage,
          };
        })(),
        rawCall: result.rawCall,
        rawResponse: result.rawResponse,
      };
    },
  };
}
