/**
 * AI Configuration Management
 * Centralized configuration for all AI providers and costs
 */

export interface AIProviderConfig {
  name: string;
  models: string[];
  envKeys: string[];
  costPerToken?: number;
  rateLimit?: {
    requests: number;
    window: number; // in seconds
  };
}

export interface AICostConfig {
  [model: string]: {
    inputCostPer1kTokens: number;
    outputCostPer1kTokens: number;
    creditsRequired: number;
  };
}

export interface AILimitsConfig {
  maxTokensPerRequest: number;
  maxRequestsPerMinute: number;
  maxDailyCredits: number;
  freeTierLimits: {
    dailyRequests: number;
    dailyTokens: number;
  };
}

/**
 * AI Provider Configurations
 */
export const AI_PROVIDERS: Record<string, AIProviderConfig> = {
  openai: {
    name: "OpenAI",
    models: ["gpt-4o", "gpt-4o-mini", "dall-e-2", "dall-e-3"],
    envKeys: ["OPENAI_API_KEY"],
    rateLimit: { requests: 60, window: 60 },
  },
  deepseek: {
    name: "DeepSeek",
    models: ["deepseek-chat", "deepseek-coder", "deepseek-r1"],
    envKeys: ["DEEPSEEK_API_KEY"],
    rateLimit: { requests: 100, window: 60 },
  },
  openrouter: {
    name: "OpenRouter",
    models: ["openrouter/auto"],
    envKeys: ["OPENROUTER_API_KEY"],
    rateLimit: { requests: 30, window: 60 },
  },
  siliconflow: {
    name: "SiliconFlow",
    models: ["siliconflow/qwen"],
    envKeys: ["SILICONFLOW_API_KEY"],
    rateLimit: { requests: 50, window: 60 },
  },
  replicate: {
    name: "Replicate",
    models: ["replicate/flux"],
    envKeys: ["REPLICATE_API_TOKEN"],
    rateLimit: { requests: 20, window: 60 },
  },
  cloudflare: {
    name: "Cloudflare Workers AI",
    models: ["@cf/meta/llama-3.1-8b-instruct", "@cf/black-forest-labs/flux-1-schnell"],
    envKeys: ["CLOUDFLARE_ACCOUNT_ID", "CLOUDFLARE_API_TOKEN"],
    rateLimit: { requests: 100, window: 60 },
  },
};

/**
 * AI Cost Configuration (in USD)
 */
export const AI_COSTS: AICostConfig = {
  // OpenAI Models
  "gpt-4o": {
    inputCostPer1kTokens: 0.005,
    outputCostPer1kTokens: 0.015,
    creditsRequired: 5,
  },
  "gpt-4o-mini": {
    inputCostPer1kTokens: 0.00015,
    outputCostPer1kTokens: 0.0006,
    creditsRequired: 1,
  },
  "dall-e-2": {
    inputCostPer1kTokens: 0.02, // per image
    outputCostPer1kTokens: 0,
    creditsRequired: 10,
  },
  "dall-e-3": {
    inputCostPer1kTokens: 0.04, // per image
    outputCostPer1kTokens: 0,
    creditsRequired: 20,
  },

  // DeepSeek Models
  "deepseek-chat": {
    inputCostPer1kTokens: 0.00014,
    outputCostPer1kTokens: 0.00028,
    creditsRequired: 1,
  },
  "deepseek-coder": {
    inputCostPer1kTokens: 0.00014,
    outputCostPer1kTokens: 0.00028,
    creditsRequired: 1,
  },
  "deepseek-r1": {
    inputCostPer1kTokens: 0.0005,
    outputCostPer1kTokens: 0.002,
    creditsRequired: 2,
  },

  // OpenRouter (averaged)
  "openrouter/auto": {
    inputCostPer1kTokens: 0.001,
    outputCostPer1kTokens: 0.002,
    creditsRequired: 2,
  },

  // SiliconFlow
  "siliconflow/qwen": {
    inputCostPer1kTokens: 0.0001,
    outputCostPer1kTokens: 0.0002,
    creditsRequired: 1,
  },

  // Replicate
  "replicate/flux": {
    inputCostPer1kTokens: 0.003, // per image
    outputCostPer1kTokens: 0,
    creditsRequired: 3,
  },

  // Cloudflare Workers AI (mostly free)
  "@cf/meta/llama-3.1-8b-instruct": {
    inputCostPer1kTokens: 0,
    outputCostPer1kTokens: 0,
    creditsRequired: 0,
  },
  "@cf/black-forest-labs/flux-1-schnell": {
    inputCostPer1kTokens: 0,
    outputCostPer1kTokens: 0,
    creditsRequired: 1,
  },
};

/**
 * AI Limits Configuration
 */
export const AI_LIMITS: AILimitsConfig = {
  maxTokensPerRequest: 4096,
  maxRequestsPerMinute: 30,
  maxDailyCredits: 1000,
  freeTierLimits: {
    dailyRequests: 50,
    dailyTokens: 10000,
  },
};

/**
 * Get configuration for a specific AI provider
 */
export function getProviderConfig(providerName: string): AIProviderConfig | null {
  return AI_PROVIDERS[providerName] || null;
}

/**
 * Get cost configuration for a specific model
 */
export function getModelCost(modelName: string) {
  return (
    AI_COSTS[modelName] || {
      inputCostPer1kTokens: 0.001,
      outputCostPer1kTokens: 0.002,
      creditsRequired: 1,
    }
  );
}

/**
 * Calculate cost for token usage
 */
export function calculateCost(
  modelName: string,
  inputTokens: number,
  outputTokens: number
): { cost: number; credits: number } {
  const costConfig = getModelCost(modelName);

  const cost =
    (inputTokens / 1000) * costConfig.inputCostPer1kTokens +
    (outputTokens / 1000) * costConfig.outputCostPer1kTokens;

  return {
    cost: Math.round(cost * 100000) / 100000, // Round to 5 decimal places
    credits: costConfig.creditsRequired,
  };
}

/**
 * Validate environment variables for a provider
 */
export function validateProviderEnv(
  providerName: string,
  env: Record<string, string | undefined>
): { isValid: boolean; missingKeys: string[] } {
  const provider = getProviderConfig(providerName);
  if (!provider) {
    return { isValid: false, missingKeys: [`Unknown provider: ${providerName}`] };
  }

  const missingKeys = provider.envKeys.filter((key) => !env[key]);
  return {
    isValid: missingKeys.length === 0,
    missingKeys,
  };
}

/**
 * Get all supported models
 */
export function getAllSupportedModels(): string[] {
  return Object.keys(AI_COSTS);
}

/**
 * Check if a model is supported
 */
export function isModelSupported(modelName: string): boolean {
  return modelName in AI_COSTS;
}

/**
 * Get provider name by model
 */
export function getProviderByModel(modelName: string): string | null {
  for (const [providerName, config] of Object.entries(AI_PROVIDERS)) {
    if (config.models.some((model) => modelName.includes(model) || model.includes(modelName))) {
      return providerName;
    }
  }
  return null;
}
