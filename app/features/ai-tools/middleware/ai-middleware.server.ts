/**
 * AI Middleware - Unified request handling for all AI operations
 * Eliminates code duplication across AI API routes
 */

import type { AppLoadContext } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { createDb } from "~/lib/db/db";
import { requireUserForAPI } from "~/features/auth/services/middleware.server";

export interface AIMiddlewareParams {
  user: {
    id: string;
    email: string;
    name?: string;
  };
  db: ReturnType<typeof createDb>;
  env: {
    [key: string]: string | undefined;
  };
  request: Request;
  context: AppLoadContext;
}

export interface AIOperationResult {
  success: boolean;
  data?: any;
  error?: string;
  usage?: {
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
    cost?: number;
  };
}

/**
 * Unified AI middleware that handles:
 * - User authentication
 * - Database connection
 * - Environment validation
 * - Credit checking and deduction
 * - Usage tracking
 * - Error handling
 */
export async function withAIMiddleware(
  request: Request,
  context: AppLoadContext,
  operation: (params: AIMiddlewareParams) => Promise<AIOperationResult>
): Promise<Response> {
  try {
    // 1. Authenticate user
    const user = await requireUserForAPI(request, context);
    if (!user) {
      return json({ error: "Authentication required" }, { status: 401 });
    }

    // 2. Create database connection
    const databaseUrl = context.cloudflare?.env?.DATABASE_URL;
    if (!databaseUrl) {
      console.error("DATABASE_URL not found in environment");
      return json({ error: "Database configuration error" }, { status: 500 });
    }

    const db = createDb(databaseUrl);

    // 3. Prepare environment variables
    const env = context.cloudflare?.env || {};

    // 4. Execute the AI operation
    const result = await operation({
      user,
      db,
      env,
      request,
      context,
    });

    if (!result.success) {
      return json({ error: result.error || "Operation failed" }, { status: 400 });
    }

    // 5. Track usage if provided
    if (result.usage) {
      await trackAIUsage(db, user.id, result.usage);
    }

    return json(result.data);
  } catch (error: any) {
    console.error("AI Middleware Error:", error);

    // Handle specific error types
    if (error.message?.includes("rate limit")) {
      return json({ error: "Rate limit exceeded. Please try again later." }, { status: 429 });
    }

    if (error.message?.includes("insufficient credits")) {
      return json({ error: "Insufficient credits. Please upgrade your plan." }, { status: 402 });
    }

    if (error.message?.includes("quota")) {
      return json({ error: "API quota exceeded. Please try again later." }, { status: 429 });
    }

    return json({ error: "An unexpected error occurred. Please try again." }, { status: 500 });
  }
}

/**
 * Track AI usage in the database
 */
async function trackAIUsage(
  db: ReturnType<typeof createDb>,
  userId: string,
  usage: NonNullable<AIOperationResult["usage"]>
) {
  try {
    // Import usage tracking table
    const { usageTracking } = await import("~/lib/db/schemas/monitoring");

    await db.insert(usageTracking).values({
      id: crypto.randomUUID(),
      userId,
      endpoint: "ai-operation", // Could be more specific based on operation type
      inputTokens: usage.inputTokens || 0,
      outputTokens: usage.outputTokens || 0,
      totalTokens: usage.totalTokens || usage.inputTokens + usage.outputTokens || 0,
      cost: usage.cost || 0,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error("Failed to track AI usage:", error);
    // Don't throw here - usage tracking failure shouldn't break the main operation
  }
}

/**
 * Validate required environment variables for AI operations
 */
export function validateAIEnvironment(
  env: Record<string, string | undefined>,
  requiredKeys: string[]
): { isValid: boolean; missingKeys: string[] } {
  const missingKeys = requiredKeys.filter((key) => !env[key]);
  return {
    isValid: missingKeys.length === 0,
    missingKeys,
  };
}

/**
 * Check if user has sufficient credits for an operation
 */
export async function checkUserCredits(
  db: ReturnType<typeof createDb>,
  userId: string,
  requiredCredits: number
): Promise<{ hasCredits: boolean; currentCredits: number }> {
  try {
    const { users } = await import("~/lib/db/schemas/users");
    const { eq } = await import("drizzle-orm");

    const user = await db
      .select({ credits: users.credits })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user.length) {
      return { hasCredits: false, currentCredits: 0 };
    }

    const currentCredits = user[0].credits || 0;
    return {
      hasCredits: currentCredits >= requiredCredits,
      currentCredits,
    };
  } catch (error) {
    console.error("Failed to check user credits:", error);
    return { hasCredits: false, currentCredits: 0 };
  }
}

/**
 * Deduct credits from user account
 */
export async function deductUserCredits(
  db: ReturnType<typeof createDb>,
  userId: string,
  creditsToDeduct: number
): Promise<boolean> {
  try {
    const { users } = await import("~/lib/db/schemas/users");
    const { eq } = await import("drizzle-orm");

    const { sql } = await import("drizzle-orm");

    const result = await db
      .update(users)
      .set({
        credits: sql`${users.credits} - ${creditsToDeduct}`,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    return true;
  } catch (error) {
    console.error("Failed to deduct user credits:", error);
    return false;
  }
}
