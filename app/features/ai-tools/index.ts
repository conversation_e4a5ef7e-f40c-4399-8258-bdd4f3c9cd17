/**
 * AI Tools Feature Module - Unified Exports
 * Provides a clean interface for importing AI-related functionality
 */

// Export all components
export * from "./components/ai-chat-interface";

// Export core services
export * from "./services/ai-providers";
export * from "./services/ai-utils";
export * from "./services/cloudflare-ai";
export * from "./services/ai-test-runner";

// Export middleware
export * from "./middleware/ai-middleware.server";

// Export configuration
export * from "./config/ai-config";

// Re-export commonly used functions with cleaner names
export {
  createAIModel,
  createAIProvider,
  type AIProvider,
} from "./services/ai-providers";

export {
  validateAIRequest,
  logAIOperation,
  calculateTokenCost,
  type AIRequest,
  type AIResponse,
} from "./services/ai-utils";

export {
  withAIMiddleware,
  checkUserCredits,
  deductUserCredits,
  type AIMiddlewareParams,
  type AIOperationResult,
} from "./middleware/ai-middleware.server";

export {
  AI_PROVIDERS,
  AI_COSTS,
  AI_LIMITS,
  getProviderConfig,
  getModelCost,
  calculateCost,
  validateProviderEnv,
  getAllSupportedModels,
  isModelSupported,
  getProviderByModel,
} from "./config/ai-config";
