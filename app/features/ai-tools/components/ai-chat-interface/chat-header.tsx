import { RefreshCw } from "lucide-react";
import { memo } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils/utils";
import type { Conversation } from "./types";

interface ChatHeaderProps {
  conversation?: Conversation;
  onRefresh?: () => void;
  isLoading?: boolean;
  className?: string;
}

export const ChatHeader = memo(function ChatHeader({
  conversation,
  onRefresh,
  isLoading = false,
  className,
}: ChatHeaderProps) {
  return (
    <div className={cn("border-b border-border/40 bg-background", className)}>
      <div className="container mx-auto max-w-4xl p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {conversation ? (
              <div className="space-y-1">
                <h1 className="text-lg font-semibold text-foreground truncate">
                  {conversation.title}
                </h1>
                <div className="flex items-center gap-2">
                  {conversation.provider && (
                    <Badge variant="outline" className="text-xs px-2 py-0.5">
                      {conversation.provider}
                    </Badge>
                  )}
                  {conversation.model && (
                    <span className="text-xs text-muted-foreground font-mono">
                      {conversation.model}
                    </span>
                  )}
                  <span className="text-xs text-muted-foreground">
                    Updated {new Date(conversation.updatedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                <h1 className="text-lg font-semibold text-foreground">New Conversation</h1>
                <p className="text-sm text-muted-foreground">
                  Start chatting to create a new conversation
                </p>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {onRefresh && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRefresh}
                disabled={isLoading}
                className="h-8 w-8 p-0"
                aria-label="Refresh conversation"
              >
                <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

export default ChatHeader;
