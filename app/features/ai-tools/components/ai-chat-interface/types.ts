// Unified type definitions for AI Chat Interface

export interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt: string;
  provider?: string;
  model?: string;
  tokenCount?: number;
  metadata?: Record<string, unknown>;
  imageUrl?: string;
  type?: "text" | "image";
}

export interface Conversation {
  id: string;
  title: string;
  model?: string;
  provider?: string;
  isArchived: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

export interface ConversationResponse {
  code: number;
  data?: {
    conversation: Conversation;
    messages: Message[];
  };
  error?: string;
}

export interface AIResponse {
  code: number;
  data?: {
    result?:
      | {
          response?: string;
        }
      | string;
    text?: string;
    model?: string;
    images?: string[];
  };
  error?: string;
}

export interface AIChatInterfaceProps {
  className?: string;
  conversationId?: string;
  user?: UserProfile;
}

// Type guard functions
export function isConversationResponse(data: unknown): data is ConversationResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    "code" in data &&
    typeof (data as ConversationResponse).code === "number"
  );
}

export function isAIResponse(data: unknown): data is AIResponse {
  return (
    typeof data === "object" &&
    data !== null &&
    "code" in data &&
    typeof (data as AIResponse).code === "number"
  );
}

export function isValidMessage(message: unknown): message is Message {
  return (
    typeof message === "object" &&
    message !== null &&
    "id" in message &&
    "role" in message &&
    "content" in message &&
    typeof (message as Message).id === "string" &&
    ["user", "assistant"].includes((message as Message).role) &&
    typeof (message as Message).content === "string"
  );
}
