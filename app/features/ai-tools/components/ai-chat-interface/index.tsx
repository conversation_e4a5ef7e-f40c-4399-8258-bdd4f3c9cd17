import { Link } from "@remix-run/react";
import { LogIn } from "lucide-react";
import { memo } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { cn } from "~/lib/utils/utils";
import ChatHeader from "./chat-header";
import ChatInput from "./chat-input";
import MessageList from "./message-list";
import type { AIChatInterfaceProps } from "./types";
import { useChat } from "./use-chat";

export const AIChatInterface = memo(function AIChatInterface({
  className,
  conversationId,
  user,
}: AIChatInterfaceProps) {
  const {
    messages,
    conversation,
    isLoading,
    isStreaming,
    error,
    sendMessage,
    refreshConversation,
    clearError,
  } = useChat({
    conversationId,
    onError: (error) => {
      console.error("Chat error:", error);
    },
  });

  // Show login prompt if user is not authenticated
  if (!user) {
    return (
      <div className={cn("flex-1 flex items-center justify-center p-8", className)}>
        <div className="text-center max-w-md">
          <LogIn className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-40" />
          <h3 className="text-lg font-semibold mb-2">Sign in to continue</h3>
          <p className="text-sm text-muted-foreground mb-6 leading-6">
            Please sign in to start chatting with AI assistants and save your conversations.
          </p>
          <Button asChild>
            <Link to="/auth/login">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Error banner */}
      {error && (
        <div className="bg-destructive/10 border-b border-destructive/20 p-3">
          <div className="container mx-auto max-w-4xl flex items-center justify-between">
            <p className="text-sm text-destructive">{error}</p>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="text-destructive hover:text-destructive"
            >
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Chat Header */}
      <ChatHeader
        conversation={conversation}
        onRefresh={refreshConversation}
        isLoading={isLoading}
      />

      {/* Message List */}
      <MessageList
        messages={messages}
        isLoading={isLoading}
        isStreaming={isStreaming}
        className="flex-1"
      />

      {/* Chat Input */}
      <ChatInput
        onSend={sendMessage}
        isLoading={isLoading || isStreaming}
        placeholder="Type your message here..."
      />
    </div>
  );
});

export default AIChatInterface;
