/**
 * Auth Feature Module - Unified Exports
 * Provides a clean interface for importing auth-related functionality
 */

// Export all components
export * from "./components/google-one-tap";
export * from "./components/protected-route";
export * from "./components/user-menu";

// Export services
export * from "./services/auth.server";
export * from "./services/session.server";
export * from "./services/middleware.server";

// Re-export commonly used functions with cleaner names
export { authenticator } from "./services/auth.server";
export { requireUser, getUser, requireUserForAPI } from "./services/middleware.server";
export { sessionStorage } from "./services/session.server";
