/**
 * Unified User Information API
 * Returns current user information with optional detailed data
 * Supports both simple and detailed user info based on ?detailed=true param
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { getUser } from "~/lib/auth/middleware.server";
import { createDbFromEnv } from "~/lib/db";
import { getUserWithRelations } from "~/models/user";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const url = new URL(request.url);
    const detailed = url.searchParams.get("detailed") === "true";

    const userResult = await getUser(request);

    if (userResult.success && userResult.user) {
      // Return basic user info by default
      let responseData = {
        id: userResult.user.id,
        uuid: userResult.user.uuid,
        email: userResult.user.email,
        name: userResult.user.name,
        avatar: userResult.user.avatar,
        credits: userResult.user.credits,
      };

      // If detailed info requested, get full user relations
      if (detailed) {
        try {
          const db = context.db || createDbFromEnv(context.cloudflare?.env);
          const userWithRelations = await getUserWithRelations(userResult.user.uuid, db);
          if (userWithRelations) {
            responseData = { ...responseData, ...userWithRelations };
          }
        } catch (error) {
          console.warn("Failed to get detailed user info:", error);
          // Continue with basic info if detailed fails
        }
      }

      return json({
        success: true,
        user: responseData,
      });
    } else {
      return json(
        {
          success: false,
          error: userResult.error || "Not authenticated",
        },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Auth me API error:", error);
    return json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
