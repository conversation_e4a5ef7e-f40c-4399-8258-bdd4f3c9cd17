import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { authenticator } from "../services/auth.server";

export async function action({ request }: ActionFunctionArgs) {
  try {
    // Authenticate with Google One Tap and redirect on success
    return await authenticator.authenticate("google-one-tap", request, {
      successRedirect: "/console",
      failureRedirect: "/auth/login?error=auth-failed",
    });
  } catch (error) {
    console.error("Google authentication error:", error);
    return redirect("/auth/login?error=auth-failed");
  }
}

// Handle GET requests (shouldn't happen, but just in case)
export async function loader() {
  return redirect("/auth/login");
}
