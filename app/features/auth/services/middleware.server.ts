/**
 * 认证中间件 - 使用 remix-auth
 */

import { redirect } from "@remix-run/cloudflare";
import type { SessionUser } from "./auth.server";
import { sessionStorage } from "./session.server";

// 认证结果接口
export interface AuthResult {
  success: boolean;
  user?: SessionUser;
  error?: string;
}

/**
 * 核心认证函数 - requireUser
 * 这是唯一需要的认证函数，所有 Loader 都调用它
 */
export async function requireUser(request: Request): Promise<SessionUser> {
  const session = await sessionStorage.getSession(request.headers.get("Cookie"));
  const user = session.get("user") as SessionUser | undefined;

  if (!user) {
    throw redirect("/auth/login");
  }

  return user;
}

/**
 * 可选的非强制认证检查
 * 返回用户信息或 null，不会重定向
 */
export async function getUser(request: Request): Promise<AuthResult> {
  try {
    const session = await sessionStorage.getSession(request.headers.get("Cookie"));
    const user = session.get("user") as SessionUser | undefined;

    if (!user) {
      return { success: false, error: "Not authenticated" };
    }

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error("Auth check error:", error);
    return { success: false, error: "Authentication failed" };
  }
}

/**
 * API专用认证函数 - 返回JSON错误而不是重定向
 * 用于API路由，不会重定向到登录页面
 */
export async function requireUserForAPI(request: Request): Promise<SessionUser> {
  const session = await sessionStorage.getSession(request.headers.get("Cookie"));
  const user = session.get("user") as SessionUser | undefined;

  if (!user) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Authentication required",
        message: "Please log in to access this resource",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  return user;
}
